import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface RecordingAnimationProps {
  audioLevel: number; // Audio level in dB (-160 to 0)
  isRecording: boolean;
}

const RecordingAnimation: React.FC<RecordingAnimationProps> = ({ audioLevel, isRecording }) => {
  // Animation values for concentric circles
  const outerCircleAnim = useRef(new Animated.Value(0)).current;
  const middleCircleAnim = useRef(new Animated.Value(0)).current;
  const innerCircleAnim = useRef(new Animated.Value(1)).current;

  // Normalize audio level to a value between 0 and 1
  const normalizedLevel = Math.min(1, Math.max(0, (audioLevel + 160) / 160));

  // Threshold for detecting if user is talking
  const TALKING_THRESHOLD = 0.5;
  const isTalking = normalizedLevel > TALKING_THRESHOLD;

  useEffect(() => {
    if (isRecording) {
      // Component is visible when recording
    } else {
      stopPulseAnimations();
    }

    return () => {
      outerCircleAnim.stopAnimation();
      middleCircleAnim.stopAnimation();
      innerCircleAnim.stopAnimation();
    };
  }, [isRecording]);

  // Control pulse animations based on talking state
  useEffect(() => {
    if (isRecording && isTalking) {
      startPulseAnimations();
    } else {
      stopPulseAnimations();
    }
  }, [isRecording, isTalking]);

  // Update animations based on audio level
  useEffect(() => {
    if (isRecording) {
      const intensity = normalizedLevel * 0.3 + 0.7; // Scale between 0.7 and 1.0
      Animated.timing(innerCircleAnim, {
        toValue: intensity,
        duration: 100,
        useNativeDriver: true,
      }).start();
    }
  }, [normalizedLevel, isRecording]);

  const startPulseAnimations = () => {
    // Outer circle pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(outerCircleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(outerCircleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Middle circle pulse animation (slightly delayed)
    Animated.loop(
      Animated.sequence([
        Animated.delay(400),
        Animated.timing(middleCircleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(middleCircleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimations = () => {
    outerCircleAnim.stopAnimation();
    middleCircleAnim.stopAnimation();

    // Reset to initial values
    outerCircleAnim.setValue(0);
    middleCircleAnim.setValue(0);
  };

  if (!isRecording) return null;

  return (
    <View style={styles.container}>
      {/* Outer circle (largest, most transparent) */}
      <Animated.View
        style={[
          styles.circle,
          styles.outerCircle,
          {
            opacity: outerCircleAnim.interpolate({
              inputRange: [0, 0.5, 1],
              outputRange: [0, 0.3, 0],
            }),
            transform: [
              {
                scale: outerCircleAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.8],
                }),
              },
            ],
          },
        ]}
      />

      {/* Middle circle */}
      <Animated.View
        style={[
          styles.circle,
          styles.middleCircle,
          {
            opacity: middleCircleAnim.interpolate({
              inputRange: [0, 0.5, 1],
              outputRange: [0, 0.5, 0],
            }),
            transform: [
              {
                scale: middleCircleAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.4],
                }),
              },
            ],
          },
        ]}
      />

      {/* Inner circle (red, solid) */}
      <Animated.View
        style={[
          styles.circle,
          styles.innerCircle,
          {
            transform: [{ scale: innerCircleAnim }],
          },
        ]}
      >
        {/* Microphone icon */}
        <Ionicons name="mic" size={40} color="white" />
      </Animated.View>

      {/* Recording text */}
      <View style={styles.textContainer}>
        <Text style={styles.recordingText}>Speak Now</Text>
        {/* <Text style={styles.levelText}>
          {normalizedLevel > 0.1 ? '🔊' : '🔇'} {(normalizedLevel * 100).toFixed(0)}%
        </Text> */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
    position: 'relative',
  },
  circle: {
    position: 'absolute',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  outerCircle: {
    width: 200,
    height: 200,
    backgroundColor: 'rgba(255, 182, 193, 0.3)', // Light pink
  },
  middleCircle: {
    width: 150,
    height: 150,
    backgroundColor: 'rgba(255, 105, 135, 0.5)', // Medium pink
  },
  innerCircle: {
    width: 100,
    height: 100,
    backgroundColor: '#FF0000', // Red
    zIndex: 3,
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 120,
    zIndex: 4,
  },
  recordingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
    textShadowColor: 'rgba(244, 67, 54, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
    fontFamily: 'EstedadRegular',
    marginTop: 20,
  },
  levelText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default RecordingAnimation;
