import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const ThinkingAnimation = () => {
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animated dots sequence
    const animateDots = () => {
      const dotAnimation = (dotAnim: Animated.Value, delay: number) => {
        return Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(dotAnim, {
              toValue: 1,
              duration: 600,
              useNativeDriver: true,
            }),
            Animated.timing(dotAnim, {
              toValue: 0,
              duration: 600,
              useNativeDriver: true,
            }),
          ])
        );
      };

      Animated.parallel([
        dotAnimation(dot1Anim, 0),
        dotAnimation(dot2Anim, 200),
        dotAnimation(dot3Anim, 400),
      ]).start();
    };

    // Pulse animation for the container
    const animatePulse = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    // Glow animation
    const animateGlow = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(glowAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateDots();
    animatePulse();
    animateGlow();

    return () => {
      dot1Anim.stopAnimation();
      dot2Anim.stopAnimation();
      dot3Anim.stopAnimation();
      pulseAnim.stopAnimation();
      glowAnim.stopAnimation();
    };
  }, []);

  const getDotStyle = (dotAnim: Animated.Value) => ({
    opacity: dotAnim,
    transform: [
      {
        scale: dotAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.5, 1.2],
        }),
      },
      {
        translateY: dotAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -8],
        }),
      },
    ],
  });

  const glowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.8],
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: pulseAnim }],
        },
      ]}
    >
      {/* Glow effect background */}
      <Animated.View
        style={[
          styles.glowBackground,
          {
            opacity: glowOpacity,
          },
        ]}
      >
        <LinearGradient
          colors={['rgba(66, 133, 244, 0.3)', 'rgba(66, 133, 244, 0.1)', 'rgba(66, 133, 244, 0.3)']}
          style={styles.glowGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      {/* Main content */}
      <View style={styles.content}>
        {/* <Text style={styles.thinkingText}>Thinking</Text> */}
        
        {/* Animated dots */}
        <View style={styles.dotsContainer}>
          <Animated.View style={[styles.dot, getDotStyle(dot1Anim)]}>
            <LinearGradient
              colors={['#4285F4', '#1a73e8']}
              style={styles.dotGradient}
            />
          </Animated.View>
          
          <Animated.View style={[styles.dot, getDotStyle(dot2Anim)]}>
            <LinearGradient
              colors={['#4285F4', '#1a73e8']}
              style={styles.dotGradient}
            />
          </Animated.View>
          
          <Animated.View style={[styles.dot, getDotStyle(dot3Anim)]}>
            <LinearGradient
              colors={['#4285F4', '#1a73e8']}
              style={styles.dotGradient}
            />
          </Animated.View>
        </View>

        {/* Brain icon with subtle animation */}
        {/* <Animated.View
          style={[
            styles.brainIcon,
            {
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.6, 1],
              }),
            },
          ]}
        >
          <Text style={styles.brainEmoji}>🧠</Text>
        </Animated.View> */}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 30,
    position: 'relative',
  },
  glowBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 25,
  },
  glowGradient: {
    flex: 1,
    borderRadius: 25,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  thinkingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    textShadowColor: 'rgba(66, 133, 244, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    marginTop: 10,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 3,
    shadowColor: '#4285F4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 5,
  },
  dotGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
  brainIcon: {
    marginTop: 4,
  },
  brainEmoji: {
    fontSize: 16,
    textShadowColor: 'rgba(255, 255, 255, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default ThinkingAnimation;
