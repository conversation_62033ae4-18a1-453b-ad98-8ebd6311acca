import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  StyleSheet,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Speech from "expo-speech";
import { getAvailableVoices } from "../../utils/voiceUtils";

interface Voice {
  id: string;
  name: string;
  language: string;
}

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectVoice: (voiceId: string) => void;
  selectedVoice: string | null;
  voices: Voice[];
  onStopRecording?: () => void;
  onStartRecording?: () => void;
  isSpeaking?: boolean;
}

const SettingsModal = ({
  visible,
  onClose,
  onSelectVoice,
  selectedVoice,
  voices,
  onStopRecording,
  onStartRecording,
  isSpeaking,
}: SettingsModalProps) => {
  const [speechRate, setSpeechRate] = useState<number>(0.8);
  const [showSpeedDropdown, setShowSpeedDropdown] = useState(false);
  const [showVoiceDropdown, setShowVoiceDropdown] = useState(false);
  const [fallbackVoices, setFallbackVoices] = useState<Voice[]>([]);

  useEffect(() => {
    const loadSpeechRate = async () => {
      try {
        const savedRate = await AsyncStorage.getItem("speechRate");
        if (savedRate) {
          setSpeechRate(parseFloat(savedRate));
        }
      } catch (error) {
        console.error("Error loading speech rate:", error);
      }
    };

    loadSpeechRate();
  }, []);

  const saveSpeechRate = async (rate: number) => {
    try {
      await AsyncStorage.setItem("speechRate", rate.toString());
      setSpeechRate(rate);
    } catch (error) {
      console.error("Error saving speech rate:", error);
    }
  };

  const playVoiceSample = (voiceId: string) => {
    // Stop any ongoing recording before playing the sample
    if (onStopRecording) {
      onStopRecording();
    }

    Speech.speak("This is a sample voice.", {
      voice: voiceId,
      rate: speechRate,
      pitch: 1,
      language: "en-US",
      onDone: () => {
        // After voice sample is done, restart recording if modal is still open
        if (visible && onStartRecording && !isSpeaking) {
          setTimeout(() => {
            onStartRecording();
          }, 300);
        }
      },
      onError: (error: any) => {
        console.error("Error playing voice sample:", error);
        // On error, also try to restart recording
        if (visible && onStartRecording && !isSpeaking) {
          setTimeout(() => {
            onStartRecording();
          }, 300);
        }
      }
    });
  };

  // Speed options
  const speedOptions = [
    { value: 0.5, label: "آهسته (0.5x)" },
    { value: 0.7, label: "کمی آهسته (0.7x)" },
    { value: 0.8, label: "عادی (0.8x)" },
    { value: 1.0, label: "طبیعی (1.0x)" },
    { value: 1.2, label: "کمی سریع (1.2x)" },
    { value: 1.5, label: "سریع (1.5x)" },
    { value: 2.0, label: "خیلی سریع (2.0x)" },
  ];

  const getSpeedLabel = (rate: number) => {
    const option = speedOptions.find((opt) => opt.value === rate);
    return option ? option.label : `${rate.toFixed(1)}x`;
  };

  const getVoiceLabel = (voiceId: string | null) => {
    if (!voiceId) return "انتخاب کنید";

    // Find the voice in the grouped voices to get the display name
    const groups = groupVoicesByAccent(displayVoices);
    for (const [, group] of Object.entries(groups)) {
      const voice = group.voices.find((v) => v.id === voiceId);
      if (voice) {
        // Get accent flag
        const language = voice.language.toLowerCase();
        let flag = "";

        if (language.includes("en-us")) {
          flag = "🇺🇸";
        } else if (language.includes("en-gb")) {
          flag = "🇬🇧";
        } else if (language.includes("en-au")) {
          flag = "🇦🇺";
        } else if (language.includes("en-in")) {
          flag = "🇮🇳";
        } else if (language.includes("en-ng")) {
          flag = "🇳🇬";
        } else if (language.includes("en-ca")) {
          flag = "🇨🇦";
        } else if (language.includes("en-za")) {
          flag = "🇿🇦";
        } else {
          flag = "🌍";
        }

        return `${flag} ${voice.displayName}`;
      }
    }

    return "انتخاب کنید";
  };

  // Get the voices to display (use fallback if main voices are empty)
  const displayVoices = voices.length > 0 ? voices : fallbackVoices;

  // Helper function to convert number to Persian
  const numberToPersian = (num: number): string => {
    const persianNumbers = [
      "صفر",
      "یک",
      "دو",
      "سه",
      "چهار",
      "پنج",
      "شش",
      "هفت",
      "هشت",
      "نه",
      "ده",
      "یازده",
      "دوازده",
      "سیزده",
      "چهارده",
      "پانزده",
      "شانزده",
      "هفده",
      "هجده",
      "نوزده",
      "بیست",
    ];

    if (num <= 20) {
      return persianNumbers[num];
    } else if (num <= 99) {
      const tens = Math.floor(num / 10);
      const ones = num % 10;
      const tensNames = [
        "",
        "",
        "بیست",
        "سی",
        "چهل",
        "پنجاه",
        "شصت",
        "هفتاد",
        "هشتاد",
        "نود",
      ];

      if (ones === 0) {
        return tensNames[tens];
      } else {
        return `${tensNames[tens]} و ${persianNumbers[ones]}`;
      }
    } else {
      return num.toString(); // Fallback for numbers > 99
    }
  };

  // Group voices by accent/region
  const groupVoicesByAccent = (voices: Voice[]) => {
    const groups: {
      [key: string]: {
        title: string;
        voices: (Voice & { displayName: string })[];
        order: number;
      };
    } = {};

    voices.forEach((voice) => {
      const language = voice.language.toLowerCase();
      let groupKey = "";
      let groupTitle = "";
      let order = 999; // Default order for other groups

      if (language.includes("en-us")) {
        groupKey = "us";
        groupTitle = "🇺🇸 آمریکایی (American)";
        order = 1;
      } else if (language.includes("en-gb")) {
        groupKey = "gb";
        groupTitle = "🇬🇧 بریتانیایی (British)";
        order = 2;
      } else if (language.includes("en-au")) {
        groupKey = "au";
        groupTitle = "🇦🇺 استرالیایی (Australian)";
        order = 3;
      } else if (language.includes("en-in")) {
        groupKey = "in";
        groupTitle = "🇮🇳 هندی (Indian)";
        order = 4;
      } else if (language.includes("en-ca")) {
        groupKey = "ca";
        groupTitle = "🇨🇦 کانادایی (Canadian)";
        order = 5;
      } else if (language.includes("en-ng")) {
        groupKey = "ng";
        groupTitle = "🇳🇬 نیجریایی (Nigerian)";
        order = 6;
      } else if (language.includes("en-za")) {
        groupKey = "za";
        groupTitle = "🇿🇦 آفریقای جنوبی (South African)";
        order = 7;
      } else {
        groupKey = "other";
        groupTitle = "🌍 سایر (Other)";
        order = 999;
      }

      if (!groups[groupKey]) {
        groups[groupKey] = { title: groupTitle, voices: [], order };
      }

      groups[groupKey].voices.push({
        ...voice,
        displayName: "", // Will be set after sorting
      });
    });

    // Sort voices within each group by name and assign display names
    Object.keys(groups).forEach((key) => {
      groups[key].voices.sort((a, b) => a.name.localeCompare(b.name));

      // Assign Persian numbered names
      groups[key].voices.forEach((voice, index) => {
        voice.displayName = `صدای شماره ${numberToPersian(index + 1)}`;
      });
    });

    // Convert to ordered array
    const orderedGroups = Object.entries(groups)
      .sort(([, a], [, b]) => a.order - b.order)
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {} as { [key: string]: { title: string; voices: (Voice & { displayName: string })[]; order: number } });

    return orderedGroups;
  };

  const voiceGroups = groupVoicesByAccent(displayVoices);

  // Load fallback voices if the passed voices array is empty
  useEffect(() => {
    const loadFallbackVoices = async () => {
      if (voices.length === 0 && visible) {
        try {
          const availableVoices = await getAvailableVoices();
          setFallbackVoices(availableVoices);
        } catch (error) {
          console.error(
            "SettingsModal - Error loading fallback voices:",
            error
          );
        }
      }
    };

    loadFallbackVoices();
  }, [voices.length, visible]);

  // Stop recording when modal opens
  useEffect(() => {
    if (visible && onStopRecording) {
      console.log("SettingsModal: Modal opened, stopping recording");
      onStopRecording();
    }
  }, [visible, onStopRecording]);

  const handleOnClose = async () => {
    console.log("SettingsModal: Closing modal and attempting to restart recording");

    // Call the original onClose function
    onClose();

    // Start recording again after closing if speech isn't playing
    if (onStartRecording) {
      // Check both the isSpeaking state and the actual speech status
      const isCurrentlySpeaking = isSpeaking || await Speech.isSpeakingAsync();

      console.log("SettingsModal: isSpeaking state:", isSpeaking, "Speech.isSpeakingAsync():", await Speech.isSpeakingAsync());

      if (!isCurrentlySpeaking) {
        console.log("SettingsModal: Restarting recording after 500ms delay");
        setTimeout(() => {
          onStartRecording();
        }, 500); // Small delay to ensure modal is fully closed
      } else {
        console.log("SettingsModal: Not restarting recording - speech is currently playing");
      }
    } else {
      console.log("SettingsModal: onStartRecording callback not available");
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={handleOnClose}
      presentationStyle="fullScreen"
    >
      <LinearGradient
        colors={["#667EEA", "#764BA2", "#5A4FCF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBackground}
      >
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>تنظیمات</Text>
              <TouchableOpacity onPress={handleOnClose} style={styles.closeButton}>
                <Ionicons name="close" size={28} color="white" />
              </TouchableOpacity>
            </View>

            {/* Speech Speed Section */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionCard}>
                <Text style={styles.sectionTitle}>سرعت گفتار</Text>

                <TouchableOpacity
                  style={styles.dropdown}
                  onPress={() => setShowSpeedDropdown(!showSpeedDropdown)}
                >
                  <Text style={styles.dropdownText}>
                    {getSpeedLabel(speechRate)}
                  </Text>
                  <Ionicons
                    name={showSpeedDropdown ? "chevron-up" : "chevron-down"}
                    size={20}
                    color="#667EEA"
                  />
                </TouchableOpacity>

                {showSpeedDropdown && (
                  <View style={styles.dropdownList}>
                    <ScrollView
                      style={styles.dropdownScrollView}
                      showsVerticalScrollIndicator={false}
                      nestedScrollEnabled={true}
                    >
                      {speedOptions.map((option) => (
                        <TouchableOpacity
                          key={option.value}
                          style={[
                            styles.dropdownItem,
                            speechRate === option.value &&
                              styles.selectedDropdownItem,
                          ]}
                          onPress={() => {
                            saveSpeechRate(option.value);
                            setShowSpeedDropdown(false);
                          }}
                        >
                          <Text
                            style={[
                              styles.dropdownItemText,
                              speechRate === option.value &&
                                styles.selectedDropdownItemText,
                            ]}
                          >
                            {option.label}
                          </Text>
                          {speechRate === option.value && (
                            <Ionicons
                              name="checkmark"
                              size={18}
                              color="#667EEA"
                            />
                          )}
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  </View>
                )}
              </View>
            </View>

            {/* Voice Selection Section */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionCard}>
                <Text style={styles.sectionTitle}>انتخاب صدا</Text>

                <TouchableOpacity
                  style={styles.dropdown}
                  onPress={() => setShowVoiceDropdown(!showVoiceDropdown)}
                >
                  <Text style={styles.dropdownText}>
                    {getVoiceLabel(selectedVoice)}
                  </Text>
                  <Ionicons
                    name={showVoiceDropdown ? "chevron-up" : "chevron-down"}
                    size={20}
                    color="#667EEA"
                  />
                </TouchableOpacity>

                {showVoiceDropdown && (
                  <View style={styles.dropdownList}>
                    <ScrollView
                      style={styles.dropdownScrollView}
                      showsVerticalScrollIndicator={false}
                      nestedScrollEnabled={true}
                    >
                      {Object.keys(voiceGroups).length === 0 ? (
                        <View style={styles.dropdownItem}>
                          <Text style={styles.dropdownItemText}>
                            هیچ صدایی در دسترس نیست
                          </Text>
                        </View>
                      ) : (
                        Object.entries(voiceGroups).map(([groupKey, group]) => (
                          <View key={groupKey}>
                            {/* Group Header */}
                            <View style={styles.groupHeader}>
                              <Text style={styles.groupHeaderText}>
                                {group.title}
                              </Text>
                            </View>

                            {/* Group Voices */}
                            {group.voices.map((voice) => (
                              <TouchableOpacity
                                key={voice.id}
                                style={[
                                  styles.dropdownItem,
                                  styles.groupedDropdownItem,
                                  selectedVoice === voice.id &&
                                    styles.selectedDropdownItem,
                                ]}
                                onPress={() => {
                                  onSelectVoice(voice.id);
                                  setShowVoiceDropdown(false);
                                }}
                              >
                                {selectedVoice === voice.id && (
                                  <Ionicons
                                    name="checkmark"
                                    size={18}
                                    color="#667EEA"
                                  />
                                )}
                                <Text
                                  style={[
                                    styles.dropdownItemText,
                                    selectedVoice === voice.id &&
                                      styles.selectedDropdownItemText,
                                  ]}
                                >
                                  {voice.displayName}
                                </Text>
                                <View style={styles.voiceActions}>
                                  <TouchableOpacity
                                    onPress={() => playVoiceSample(voice.id)}
                                    style={styles.playButtonSmall}
                                  >
                                    <Ionicons
                                      name="play"
                                      size={16}
                                      color="#667EEA"
                                    />
                                  </TouchableOpacity>
                                </View>
                              </TouchableOpacity>
                            ))}
                          </View>
                        ))
                      )}
                    </ScrollView>
                  </View>
                )}
              </View>
            </View>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </Modal>
  );
};

const styles = StyleSheet.create({
  gradientBackground: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
    paddingTop: 10,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: "EstedadBold",
    color: "white",
    textAlign: "center",
    flex: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    right: 0,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  sectionContainer: {
    marginBottom: 30,
  },
  sectionCard: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: "EstedadBold",
    color: "white",
    marginBottom: 20,
    textAlign: "center",
  },
  dropdown: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  dropdownText: {
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#333",
    flex: 1,
    textAlign: "right",
  },
  dropdownList: {
    backgroundColor: "rgba(255, 255, 255, 0.98)",
    borderRadius: 8,
    marginTop: 4,
    maxHeight: 200,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    overflow: "hidden",
  },
  dropdownScrollView: {
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 14,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomWidth: 0.5,
    borderBottomColor: "rgba(0, 0, 0, 0.08)",
    minHeight: 48,
  },
  selectedDropdownItem: {
    backgroundColor: "rgba(102, 126, 234, 0.08)",
  },
  dropdownItemText: {
    fontSize: 15,
    fontFamily: "EstedadRegular",
    color: "#333",
    flex: 1,
    textAlign: "right",
    lineHeight: 20,
    marginRight: 10,
  },
  selectedDropdownItemText: {
    color: "#667EEA",
    fontFamily: "EstedadBold",
  },
  voiceActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  playButtonSmall: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: "rgba(102, 126, 234, 0.08)",
    minWidth: 32,
    minHeight: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  groupHeader: {
    backgroundColor: "rgba(102, 126, 234, 0.15)",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: "rgba(102, 126, 234, 0.2)",
  },
  groupHeaderText: {
    fontSize: 14,
    fontFamily: "EstedadBold",
    color: "#667EEA",
    textAlign: "right",
  },
  groupedDropdownItem: {
    paddingLeft: 24,
    backgroundColor: "rgba(255, 255, 255, 0.02)",
  },
});

export default SettingsModal;
