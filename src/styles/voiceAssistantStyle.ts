import {
  StyleSheet,
} from "react-native";

export default StyleSheet.create({
  userSpeakingBubble: {
    position: 'absolute',
    bottom: -20,
    alignSelf: 'center',
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  userWavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  userWave: {
    width: 3,
    marginHorizontal: 1,
    backgroundColor: '#ffffff',
    borderRadius: 2,
  },
  userWave1: {
    height: 10,
    opacity: 0.4,
  },
  userWave2: {
    height: 16,
    opacity: 0.7,
  },
  userWave3: {
    height: 12,
    opacity: 0.5,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  timer: {
    color: '#4caf50',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
  },
  avatarContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 30,
  },
  avatarCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  avatar: {
    width: '50%',
    height: '50%',
    borderRadius: 100,
  },
  speakingBubble: {
    position: 'absolute',
    top: 20,
    right: -20,
    backgroundColor: '#4285F4',
    borderRadius: 25,
    padding: 10,
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  wavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 30,
  },
  wave: {
    width: 6,
    backgroundColor: 'white',
    borderRadius: 3,
  },
  wave1: {
    height: 10,
  },
  wave2: {
    height: 16,
  },
  wave3: {
    height: 10,
  },
  textContainer: {
    alignItems: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
    minHeight: 120,
    justifyContent: 'center',
    // flex: 1,
  },
  responseText: {
    color: 'white',
    fontSize: 20,
    textAlign: 'center',
    marginBottom: 5,
    lineHeight: 25,
    // fontFamily: 'System',
    fontWeight: '500',
    letterSpacing: 0.3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  translatedText: {
    color: 'rgba(255, 255, 255, 0.85)',
    fontSize: 18,
    textAlign: 'center',
    fontFamily: 'Estedad-Regular',
    lineHeight: 26,
    marginTop: 8,
  },
  translationContainer: {
    marginTop: 7,
    padding: 16,
    backgroundColor: 'rgba(30, 58, 138, 0.8)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.3)',
    shadowColor: '#1e3a8a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 3,
  },
  translationText: {
    color: 'rgba(255, 255, 255, 0.95)',
    fontSize: 17,
    textAlign: 'center',
    fontFamily: 'EstedadRegular',
    lineHeight: 24,
    letterSpacing: 0.3,
  },
  translationLabel: {
    color: 'rgba(147, 197, 253, 0.8)',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 6,
    textTransform: 'uppercase',
    letterSpacing: 1,
    fontFamily: 'EstedadRegular',
  },
  grammarCorrectionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(156, 163, 175, 0.3)',
    paddingTop: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 8,
    padding: 12,
  },
  grammarCorrectionLabel: {
    color: 'rgba(34, 197, 94, 0.8)',
    fontSize: 11,
    fontFamily: 'System',
    marginBottom: 6,
    textTransform: 'uppercase',
    letterSpacing: 1,
    alignSelf: 'flex-start',
    width: '100%',
  },
  grammarCorrectedWord: {
    fontSize: 16,
    color: '#22c55e',
    fontWeight: '600',
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    marginHorizontal: 1,
  },
  grammarOriginalWord: {
    fontSize: 16,
    color: 'rgba(156, 163, 175, 0.8)',
    fontWeight: '400',
    marginHorizontal: 1,
  },
  interruptArea: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  interruptText: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 14,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  hangupButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#ef4444',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  primaryButton: {
    backgroundColor: '#F44336',
  },
  loadingText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 10,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    marginTop: 10,
  },
  userMessageWrapper: {
    alignItems: 'center',
  },
  userMessageTitleContainer: {
    position: 'absolute',
    top: -12,
    right: 15,
    backgroundColor: 'rgba(7, 86, 212, 1)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.3)',
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1,
  },
  userMessageTitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    fontFamily: 'EstedadRegular',
  },
  userMessageContainer: {
    position: 'relative',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderRadius: 12,
    padding: 12,
    paddingTop: 25,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
    marginBottom: 50,
  },
  userMessageLabel: {
    color: 'rgba(147, 197, 253, 0.8)',
    fontSize: 11,
    fontFamily: 'System',
    marginBottom: 6,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  userMessageText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    fontFamily: 'EstedadRegular',
  },
  pulsingDots: {
    fontSize: 20,
    color: 'rgba(147, 197, 253, 0.8)',
    textAlign: 'center',
  },
  enhancedContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  grammarErrorContainer: {
    // backgroundColor: 'rgba(245, 158, 11, 0.12)',
    // borderRadius: 20,
    padding: 24,
    marginVertical: 12,
    // borderWidth: 1.5,
    // borderColor: 'rgba(245, 158, 11, 0.4)',
    // shadowColor: '#f59e0b',
    // shadowOffset: { width: 0, height: 6 },
    // shadowOpacity: 0.25,
    shadowRadius: 12,
    // elevation: 6,
    // Add a subtle inner glow effect
    position: 'relative',
  },
  grammarErrorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    justifyContent: 'center',
  },
  grammarErrorTitle: {
    color: '#fbbf24',
    fontSize: 19,
    fontWeight: '700',
    marginLeft: 8,
    fontFamily: 'EstedadBold',
    textAlign: 'center',
    textShadowColor: 'rgba(251, 191, 36, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  grammarErrorSubtitle: {
    color: 'rgba(255, 255, 255, 0.85)',
    fontSize: 15,
    textAlign: 'center',
    marginBottom: 18,
    fontFamily: 'EstedadRegular',
    lineHeight: 22,
    letterSpacing: 0.3,
  },
  grammarErrorTextContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.15)',
    borderRadius: 12,
    minHeight: 50,
  },
  grammarErrorText: {
    fontSize: 19,
    lineHeight: 28,
    textAlign: 'center',
    fontFamily: 'System',
    letterSpacing: 0.4,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  grammarErrorFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(245, 158, 11, 0.25)',
    marginTop: 4,
  },
  grammarErrorFooterContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  grammarErrorFooterText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    marginLeft: 8,
    fontFamily: 'EstedadRegular',
    textAlign: 'center',
    letterSpacing: 0.2,
  },
  grammarErrorPulse: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(34, 197, 94, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  grammarErrorPulseInner: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#22c55e',
  },
});
