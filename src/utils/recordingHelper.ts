import { Audio } from "expo-av";
import { MutableRefObject } from "react";
import { stopSpeech, isSpeaking } from "./speechUtils";
import RNFS from "react-native-fs";

// Types
export interface RecordingState {
  isRecording: boolean;
  transcribing: boolean;
}

export interface RecordingRefs {
  recordingRef: MutableRefObject<Audio.Recording | null>;
  stopTimeoutRef: MutableRefObject<NodeJS.Timeout | null>;
  silenceTimeoutRef: MutableRefObject<NodeJS.Timeout | null>;
  silenceStopTimerRef: MutableRefObject<NodeJS.Timeout | null>;
  lastAudioLevelRef: MutableRefObject<number>;
  voiceActivityDetectedRef: MutableRefObject<boolean>;
  hasStartedRef: MutableRefObject<boolean>;
}

export interface RecordingCallbacks {
  setIsRecording: (recording: boolean) => void;
  setTranscribing: (transcribing: boolean) => void;
  onRecordingComplete: (uri: string) => void;
  Speech: { stop: () => void; isSpeakingAsync: () => Promise<boolean> };
  setIsSpeaking: (speaking: any) => void;
  onAudioLevelChange?: (level: number) => void; // Callback for audio level changes
}

// Constants
export const SILENCE_THRESHOLD_DB_START = -50; // dBFS: 0 = loudest, negative = quieter
export const SILENCE_THRESHOLD_DB_STOP  = -65; // dBFS: 0 = loudest, negative = quieter

export const SILENCE_DURATION = 2500;
export const AUDIO_LEVEL_CHECK_INTERVAL = 100; // Check audio level every 100ms
export const AUTO_STOP_DURATION = 30000; // 30 seconds auto-stop

// Permission handling
export async function checkAndRequestMicrophonePermission(): Promise<boolean> {
  const permission = await Audio.getPermissionsAsync();
  if (permission.granted) {
    return true;
  }
  const response = await Audio.requestPermissionsAsync();
  return response.granted;
}

// Audio configuration
export async function configureAudioMode(): Promise<void> {
  await Audio.setAudioModeAsync({
    allowsRecordingIOS: true,
    playsInSilentModeIOS: true,
  });
}

export async function resetAudioMode(): Promise<void> {
  await Audio.setAudioModeAsync({
    allowsRecordingIOS: false,
  });
}

// Recording options
export function getRecordingOptions() {
  return {
    ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
    android: {
      ...Audio.RecordingOptionsPresets.HIGH_QUALITY.android,
      extension: ".mp3",
      outputFormat: Audio.AndroidOutputFormat.MPEG_4,
      audioEncoder: Audio.AndroidAudioEncoder.AAC,
    },
    ios: {
      ...Audio.RecordingOptionsPresets.HIGH_QUALITY.ios,
      extension: ".mp3",
      outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
      audioQuality: Audio.IOSAudioQuality.HIGH,
    },
    isMeteringEnabled: true, // Enable metering for silence detection
  };
}

// Timeout management
export function clearAllTimeouts(refs: RecordingRefs): void {
  if (refs.stopTimeoutRef.current) {
    clearTimeout(refs.stopTimeoutRef.current);
    refs.stopTimeoutRef.current = null;
  }
  if (refs.silenceTimeoutRef.current) {
    clearTimeout(refs.silenceTimeoutRef.current);
    refs.silenceTimeoutRef.current = null;
  }
  if (refs.silenceStopTimerRef.current) {
    clearTimeout(refs.silenceStopTimerRef.current);
    refs.silenceStopTimerRef.current = null;
  }
}

// Silence detection
export function createSilenceDetectionCallback(
  refs: RecordingRefs,
  callbacks: RecordingCallbacks,
  autoStart: boolean
) {
  return async () => {
    if (!refs.recordingRef.current) return;

    try {
      const status = await refs.recordingRef.current.getStatusAsync();
      if (status.isRecording) {
        const levelDb = status.metering; // -160..0 if metering is enabled
        
        // Update the last audio level ref for visualizer
        refs.lastAudioLevelRef.current = typeof levelDb === "number" ? levelDb : refs.lastAudioLevelRef.current;
        
        // Detect voice activity when level exceeds threshold
        if (typeof levelDb === "number" && levelDb > SILENCE_THRESHOLD_DB_START) {
          // console.log("Voice detected at level ", levelDb);
          if (!refs.voiceActivityDetectedRef.current) {
            refs.voiceActivityDetectedRef.current = true;
            // Start a hard stop timer only after voice starts
            if (!refs.stopTimeoutRef.current) {
              console.log("Starting recording");
              refs.stopTimeoutRef.current = setTimeout(() => {
                stopRecording(refs, callbacks, autoStart);
              }, AUTO_STOP_DURATION);
            }
          }
          // If we hear audio, cancel any pending silence stop
          if (refs.silenceStopTimerRef.current) {
            clearTimeout(refs.silenceStopTimerRef.current);
            refs.silenceStopTimerRef.current = null;
          }
        } else if (
          typeof levelDb === "number" &&
          levelDb <= SILENCE_THRESHOLD_DB_STOP &&
          refs.voiceActivityDetectedRef.current
        ) {
          // Only stop on trailing silence after we've detected voice
          if (!refs.silenceStopTimerRef.current) {
            refs.silenceStopTimerRef.current = setTimeout(() => {
              if (refs.recordingRef.current) {
                stopRecording(refs, callbacks, autoStart);
              }
            }, SILENCE_DURATION);
          }
        }

        refs.lastAudioLevelRef.current =
          typeof levelDb === "number" ? levelDb : refs.lastAudioLevelRef.current;

        // Call audio level change callback if provided
        if (callbacks.onAudioLevelChange && typeof levelDb === "number") {
          callbacks.onAudioLevelChange(levelDb);
        }

        // Schedule next check only if still recording
        if (refs.recordingRef.current) {
          refs.silenceTimeoutRef.current = setTimeout(
            createSilenceDetectionCallback(refs, callbacks, autoStart),
            AUDIO_LEVEL_CHECK_INTERVAL
          );
        }
      }
    } catch (error) {
      console.error("Error checking audio level:", error);
      // Don't schedule next check on error to prevent infinite error loops
    }
  };
}

// Reset silence detection variables
export function resetSilenceDetection(refs: RecordingRefs): void {
  if (refs.silenceStopTimerRef.current) {
    clearTimeout(refs.silenceStopTimerRef.current);
    refs.silenceStopTimerRef.current = null;
  }
  refs.lastAudioLevelRef.current = 0;
  refs.voiceActivityDetectedRef.current = false;
}

// Start recording
export async function startRecording(
  refs: RecordingRefs,
  callbacks: RecordingCallbacks,
  autoStart: boolean
): Promise<void> {
  // Ensure any existing recording is properly cleaned up
  if (refs.recordingRef.current) {
    try {
      await refs.recordingRef.current.stopAndUnloadAsync();
    } catch (error) {
      console.warn("Error cleaning up existing recording:", error);
    }
    refs.recordingRef.current = null;
  }

  // Clear all timeouts to prevent conflicts
  clearAllTimeouts(refs);

  stopSpeech();
  callbacks.setIsSpeaking(null);
  
  const hasPermission = await checkAndRequestMicrophonePermission();
  if (!hasPermission) {
    console.error("No microphone permission granted");
    return;
  }

  try {
    await configureAudioMode();

    const recordingOptions = getRecordingOptions();
    const { recording } = await Audio.Recording.createAsync(recordingOptions);
    
    refs.recordingRef.current = recording;
    callbacks.setIsRecording(true);

    // Reset silence detection variables
    resetSilenceDetection(refs);
    
    // Reset hasStartedRef to allow future restarts

    // Start silence detection
    if (autoStart) {
      refs.silenceTimeoutRef.current = setTimeout(
        createSilenceDetectionCallback(refs, callbacks, autoStart),
        AUDIO_LEVEL_CHECK_INTERVAL
      );
    }
  } catch (error) {
    console.error("Error starting recording:", error);
    callbacks.setIsRecording(false); // Reset state on error
    // Clean up on error
    if (refs.recordingRef.current) {
      refs.recordingRef.current = null;
    }
  }
}

// Stop recording
export async function stopRecording(
  refs: RecordingRefs,
  callbacks: RecordingCallbacks,
  autoStart: boolean
): Promise<void> {
  if (!refs.recordingRef.current) {
    console.log("No active recording to stop.");
    return;
  }

  // Clear all timeouts
  clearAllTimeouts(refs);

  const willSend = refs.voiceActivityDetectedRef.current; // Only send if we detected voice

  if (willSend || !autoStart) {
    callbacks.setTranscribing(true); // Indicate transcription process starts
  }
  callbacks.setIsRecording(false); // Update UI state

  try {
    await refs.recordingRef.current.stopAndUnloadAsync();
    const uri = refs.recordingRef.current.getURI(); // Get the URI of the recorded file

    // Log recording size
    if (uri) {
      RNFS.stat(uri).then(stat => {
        console.log('Recording size:', stat.size, 'bytes');
      }).catch(err => {
        console.error('Error getting file size:', err);
      });
    }

    refs.recordingRef.current = null; // Clear the ref

    // Store the detected voice state before resetting
    const detectedVoice = refs.voiceActivityDetectedRef.current;
    
    // Reset silence detection variables
    resetSilenceDetection(refs);
    
    // Reset auto-start flag
    refs.hasStartedRef.current = false;

    // Reset audio mode
    await resetAudioMode();
    
    if (uri && callbacks.onRecordingComplete && (detectedVoice || !autoStart)) {
       callbacks.onRecordingComplete(uri); // Pass the URI to the callback
    }
  } catch (error) {
    console.error("Error stopping recording:", error);
  } finally {
    callbacks.setTranscribing(false); // Transcription process finished (or did not run)
  }
}

// Cleanup function
export async function cleanup(refs: RecordingRefs): Promise<void> {
  if (refs.recordingRef.current) {
    try {
      await refs.recordingRef.current.stopAndUnloadAsync(); // Ensure recording is stopped and unloaded
    } catch (error) {
      console.warn("Error during cleanup:", error);
    }
    refs.recordingRef.current = null;
  }
  clearAllTimeouts(refs);
}

// Auto-start logic
export async function checkAndStartAutoRecording(
  refs: RecordingRefs,
  callbacks: RecordingCallbacks,
  autoStart: boolean,
  isRecording: boolean,
  isSpeaking: boolean,
  transcriptionResult: any
): Promise<void> {
  
  if (transcriptionResult.length > 2) {
    const isSpeakingAsync = await callbacks.Speech.isSpeakingAsync();
    if (autoStart && !isSpeakingAsync && !isRecording && !refs.hasStartedRef.current) {
      console.log("Auto-starting recording...");
      refs.hasStartedRef.current = true;
      await startRecording(refs, callbacks, autoStart);
    }
  }
}
