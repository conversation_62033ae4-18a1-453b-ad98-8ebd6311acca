import React, { useEffect } from "react";
import {
  View,
  Button,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
} from "react-native";
import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from "@react-native-google-signin/google-signin";
import { Provider, useDispatch, useSelector } from "react-redux";
import { login } from "../store/slices/authSlice";
import RequestHelper from "../utils/requestHelper";
import { LinearGradient } from 'expo-linear-gradient';

const App = () => {
  useEffect(() => {
    configureGoogleSignIn();
  }, []);
  const dispatch = useDispatch();

  const configureGoogleSignIn = async () => {
    try {
      GoogleSignin.configure({
        webClientId:
          "527742867833-sdlunmqtsqo2rve3so9s45b4b0ftu7cq.apps.googleusercontent.com",
        // offlineAccess: false,
      });
    } catch (error) {
      console.error("Error configuring Google Sign-In:", error);
    }
  };

  const handleSignIn = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      const {
        access_token: token,
        level,
        userData,
      } = await RequestHelper(
        "post",
        `/auth/google`,
        {
          email: userInfo.user.email,
          idToken: userInfo.idToken,
          name: userInfo.user.name,
          familyName: userInfo.user.familyName,
        },
        null,
        null,
        true
      );
      dispatch(login({ ...userInfo, token, level, userData }));
    } catch (error) {
      if (error?.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log("User cancelled the sign-in process");
      } else if (error?.code === statusCodes.IN_PROGRESS) {
        console.log("Sign-in is already in progress");
      } else if (error?.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log("Play services are not available");
      } else {
        console.error("Error signing in with Google:", error);
      }
    }
  };

  return (
    <LinearGradient
      colors={['#667EEA', '#764BA2']}
      style={{ flex: 1 }}
    >
      <View
        style={{
          flex: 1,
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <GoogleSigninButton
          // style={{ width: "60%", height: 50 }}
          size={GoogleSigninButton.Size.Wide}
          color={GoogleSigninButton.Color.Dark}
          onPress={handleSignIn}
        />
        <View
          style={{
            marginTop: 30,
            width: "80%",
          }}
        >
          <Text style={styles.policy}>
            با عضویت در اسپیک آپ{" "}
            <Text
              onPress={() => {
                Linking.openURL("https://speakupai.ir/policy.html");
              }}
              style={{ textDecorationLine: "underline" }}
            >
              قوانین
            </Text>
            {" و "}
            <Text
              onPress={() => {
                Linking.openURL("https://speakupai.ir/privacy.html");
              }}
              style={{ textDecorationLine: "underline" }}
            >
              سیاست حریم خصوصی
            </Text>{" "}
            را می‌پذیرم
          </Text>
          <TouchableOpacity></TouchableOpacity>

          <Text style={styles.policy}></Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  policy: {
    fontSize: 15.5,
    color: "#fff",
    fontFamily: "EstedadRegular",
    textAlign: "center",
    paddingHorizontal: 40,
    letterSpacing: 2,
  },
});

export default App;
